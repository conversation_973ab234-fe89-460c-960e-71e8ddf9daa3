/**
 * Lazy-loaded Firebase service for Ottawa Shine Solutions
 * Reduces initial bundle size by loading Firebase modules only when needed
 */

import type { QuoteSubmission, ContactFormData, DiscountCode, DiscountCodeWithId, PricingConfiguration } from '../types';

// Firebase configuration from environment variables
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
};

// Lazy initialization promises
let firebaseApp: any = null;
let firestoreDb: any = null;
let firebaseAuth: any = null;

/**
 * Lazy initialize Firebase app
 */
async function initializeFirebaseApp() {
  if (firebaseApp) return firebaseApp;
  
  const { initializeApp } = await import('firebase/app');
  firebaseApp = initializeApp(firebaseConfig);
  return firebaseApp;
}

/**
 * Lazy initialize Firestore
 */
async function initializeFirestore() {
  if (firestoreDb) return firestoreDb;
  
  const app = await initializeFirebaseApp();
  const { getFirestore } = await import('firebase/firestore');
  firestoreDb = getFirestore(app);
  return firestoreDb;
}

/**
 * Lazy initialize Firebase Auth
 */
async function initializeAuth() {
  if (firebaseAuth) return firebaseAuth;
  
  const app = await initializeFirebaseApp();
  const { getAuth } = await import('firebase/auth');
  firebaseAuth = getAuth(app);
  return firebaseAuth;
}

/**
 * Lazy-loaded Firebase service
 */
export const firebaseLazyService = {
  // Add quote request to Firestore
  addQuoteRequest: async (quoteData: QuoteSubmission) => {
    try {
      const db = await initializeFirestore();
      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      
      // Validate required fields
      const requiredFields = ['name', 'email', 'phone', 'serviceType', 'propertySize', 'frequency'];
      for (const field of requiredFields) {
        if (!quoteData[field as keyof QuoteSubmission]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(quoteData.email)) {
        throw new Error('Invalid email format');
      }

      const docRef = await addDoc(collection(db, 'quotes'), {
        ...quoteData,
        createdAt: serverTimestamp(),
        status: 'pending'
      });

      console.log('Quote request added with ID: ', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('Error adding quote request: ', error);
      throw error;
    }
  },

  // Add contact form submission
  addContactSubmission: async (contactData: ContactFormData) => {
    try {
      const db = await initializeFirestore();
      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      
      const docRef = await addDoc(collection(db, 'contacts'), {
        ...contactData,
        createdAt: serverTimestamp(),
        status: 'unread'
      });

      console.log('Contact submission added with ID: ', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('Error adding contact submission: ', error);
      throw error;
    }
  },

  // Validate discount code
  validateDiscountCode: async (code: string): Promise<DiscountCodeWithId | null> => {
    try {
      const db = await initializeFirestore();
      const { collection, query, where, getDocs } = await import('firebase/firestore');
      
      const q = query(
        collection(db, 'discountCodes'),
        where('code', '==', code.toUpperCase()),
        where('isActive', '==', true)
      );
      
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }
      
      const doc = querySnapshot.docs[0];
      const discountData = doc.data() as DiscountCode;
      
      // Check if code is expired
      if (discountData.expiresAt && discountData.expiresAt.toDate() < new Date()) {
        return null;
      }
      
      return {
        ...discountData,
        id: doc.id,
        code: code.toUpperCase()
      } as DiscountCodeWithId;
    } catch (error) {
      console.error('Error validating discount code: ', error);
      return null;
    }
  },

  // Auth methods (lazy loaded)
  signInWithGoogle: async () => {
    try {
      const auth = await initializeAuth();
      const { GoogleAuthProvider, signInWithPopup } = await import('firebase/auth');
      
      const provider = new GoogleAuthProvider();
      const result = await signInWithPopup(auth, provider);
      return result.user;
    } catch (error) {
      console.error('Error signing in with Google: ', error);
      throw error;
    }
  },

  signOut: async () => {
    try {
      const auth = await initializeAuth();
      const { signOut } = await import('firebase/auth');
      
      await signOut(auth);
    } catch (error) {
      console.error('Error signing out: ', error);
      throw error;
    }
  },

  // Auth state observer
  onAuthStateChanged: async (callback: (user: any) => void) => {
    try {
      const auth = await initializeAuth();
      const { onAuthStateChanged } = await import('firebase/auth');
      
      return onAuthStateChanged(auth, callback);
    } catch (error) {
      console.error('Error setting up auth state observer: ', error);
      throw error;
    }
  },

  // Get pricing configuration (admin only)
  getPricingConfiguration: async (): Promise<PricingConfiguration | null> => {
    try {
      const db = await initializeFirestore();
      const { doc, getDoc } = await import('firebase/firestore');
      
      const docRef = doc(db, 'configuration', 'pricing');
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return docSnap.data() as PricingConfiguration;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting pricing configuration: ', error);
      return null;
    }
  }
};

// Export the lazy service as default
export default firebaseLazyService;
