{"name": "ottawa-shine-solutions", "private": true, "version": "1.4.0", "description": "Professional cleaning services website for Ottawa Shine Solutions", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "deploy": "npm run build && firebase deploy", "deploy:hosting": "npm run build && firebase deploy --only hosting", "deploy:rules": "firebase deploy --only firestore:rules", "deploy:quick": "npm run build && firebase deploy --only hosting", "check-build": "npm run build && npm run preview", "analyze": "npm run build && npx vite-bundle-analyzer dist", "build:analyze": "ANALYZE=true npm run build", "audit:performance": "npm run build && node scripts/performance-audit.js", "build:production": "npm run build && npm run audit:performance", "analyze:deps": "npx depcheck", "clean:deps": "npm prune"}, "dependencies": {"firebase": "^11.9.1", "lucide-react": "^0.522.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "terser": "^5.43.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.4"}}